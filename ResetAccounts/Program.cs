using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Http;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System.Text;

namespace ResetAccounts;

public class Program
{
    private static readonly HttpClient httpClient = new();
    private static ILogger<Program>? logger;

    public static async Task Main(string[] args)
    {
        // Setup logging
        var serviceProvider = new ServiceCollection()
            .AddLogging(builder => builder.AddConsole().SetMinimumLevel(LogLevel.Information))
            .BuildServiceProvider();

        logger = serviceProvider.GetService<ILogger<Program>>();

        try
        {
            // if (args.Length < 2)
            // {
            //     ShowUsage();
            //     return;
            // }

            var environment = "production";
            var emailsFile = "emails.txt";

            if (environment != "staging" && environment != "production")
            {
                logger?.LogError("Invalid environment. Use 'staging' or 'production'");
                ShowUsage();
                return;
            }

            if (!File.Exists(emailsFile))
            {
                logger?.LogError($"Email file not found: {emailsFile}");
                return;
            }

            var baseUrl = GetBaseUrl(environment);
            logger?.LogInformation($"Starting password reset process for {environment} environment");
            logger?.LogInformation($"API Endpoint: {baseUrl}");

            var emails = await ReadEmailsFromFile(emailsFile);
            logger?.LogInformation($"Found {emails.Count} email addresses to process");

            if (emails.Count == 0)
            {
                logger?.LogWarning("No email addresses found in the file");
                return;
            }

            // Confirm before proceeding
            Console.WriteLine($"\nYou are about to reset passwords for {emails.Count} accounts in {environment.ToUpper()} environment.");
            Console.Write("Do you want to continue? (y/N): ");
            var confirmation = Console.ReadLine();

            if (confirmation?.ToLower() != "y" && confirmation?.ToLower() != "yes")
            {
                logger?.LogInformation("Operation cancelled by user");
                return;
            }

            await ProcessPasswordResets(baseUrl, emails);
        }
        catch (Exception ex)
        {
            logger?.LogError(ex, "An unexpected error occurred");
        }
        finally
        {
            await serviceProvider.DisposeAsync();
        }
    }

    private static void ShowUsage()
    {
        Console.WriteLine("Usage: ResetAccounts <environment> <emails-file>");
        Console.WriteLine("  environment: 'staging' or 'production'");
        Console.WriteLine("  emails-file: Path to text file containing email addresses (one per line)");
        Console.WriteLine();
        Console.WriteLine("Example: ResetAccounts staging emails.txt");
    }

    private static string GetBaseUrl(string environment)
    {
        return environment.ToLower() switch
        {
            "staging" => "https://user-apollo.staging.dehavilland.co.uk",
            "production" => "https://user-apollo.production.live.dehavilland.co.uk",
            _ => throw new ArgumentException($"Invalid environment: {environment}")
        };
    }

    private static async Task<List<string>> ReadEmailsFromFile(string filePath)
    {
        var emails = new List<string>();
        var lines = await File.ReadAllLinesAsync(filePath);

        foreach (var line in lines)
        {
            var email = line.Trim();
            if (!string.IsNullOrEmpty(email) && IsValidEmail(email))
            {
                emails.Add(email);
            }
            else if (!string.IsNullOrEmpty(email))
            {
                logger?.LogWarning($"Skipping invalid email format: {email}");
            }
        }

        return emails;
    }

    private static bool IsValidEmail(string email)
    {
        try
        {
            var addr = new System.Net.Mail.MailAddress(email);
            return addr.Address == email;
        }
        catch
        {
            return false;
        }
    }

    private static async Task ProcessPasswordResets(string baseUrl, List<string> emails)
    {
        var endpoint = $"{baseUrl}/user-api/users/send-password-reset-email";
        var successCount = 0;
        var failureCount = 0;
        var results = new List<ResetResult>();

        logger?.LogInformation($"Processing {emails.Count} password reset requests...");
        Console.WriteLine();

        for (int i = 0; i < emails.Count; i++)
        {
            var email = emails[i];
            var result = await ResetPassword(endpoint, email, i + 1, emails.Count);
            results.Add(result);

            if (result.Success)
            {
                successCount++;
                logger?.LogInformation($"✓ [{i + 1}/{emails.Count}] Successfully reset password for: {email}");
            }
            else
            {
                failureCount++;
                logger?.LogError($"✗ [{i + 1}/{emails.Count}] Failed to reset password for: {email} - {result.ErrorMessage}");
            }

            // Add a small delay to avoid overwhelming the API
            if (i < emails.Count - 1)
            {
                await Task.Delay(500);
            }
        }

        // Summary
        Console.WriteLine();
        logger?.LogInformation("=== SUMMARY ===");
        logger?.LogInformation($"Total processed: {emails.Count}");
        logger?.LogInformation($"Successful: {successCount}");
        logger?.LogInformation($"Failed: {failureCount}");

        if (failureCount > 0)
        {
            Console.WriteLine();
            logger?.LogWarning("Failed email addresses:");
            foreach (var result in results.Where(r => !r.Success))
            {
                logger?.LogWarning($"  {result.Email}: {result.ErrorMessage}");
            }
        }

        // Write results to log file
        await WriteResultsToFile(results);
    }

    private static async Task<ResetResult> ResetPassword(string endpoint, string email, int current, int total)
    {
        try
        {
            var requestBody = new { email = email };
            var json = JsonConvert.SerializeObject(requestBody);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            string jwtToken =
                "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.kfdnT6y6oFZqRMBP5HTeEbcSO9MyqTdwzZrhZbqwKZc";
            
            httpClient.DefaultRequestHeaders.Clear();
            httpClient.DefaultRequestHeaders.Add("accept", "*/*");
            httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {jwtToken}");

            var response = await httpClient.PostAsync(endpoint, content);

            if (response.IsSuccessStatusCode)
            {
                return new ResetResult { Email = email, Success = true };
            }
            else
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                return new ResetResult
                {
                    Email = email,
                    Success = false,
                    ErrorMessage = $"HTTP {(int)response.StatusCode}: {responseContent}"
                };
            }
        }
        catch (Exception ex)
        {
            return new ResetResult
            {
                Email = email,
                Success = false,
                ErrorMessage = ex.Message
            };
        }
    }

    private static async Task WriteResultsToFile(List<ResetResult> results)
    {
        var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
        var logFileName = $"reset_results_{timestamp}.log";

        var logContent = new StringBuilder();
        logContent.AppendLine($"Password Reset Results - {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
        logContent.AppendLine("=".PadRight(50, '='));
        logContent.AppendLine();

        foreach (var result in results)
        {
            var status = result.Success ? "SUCCESS" : "FAILED";
            logContent.AppendLine($"{status}: {result.Email}");
            if (!result.Success)
            {
                logContent.AppendLine($"  Error: {result.ErrorMessage}");
            }
        }

        logContent.AppendLine();
        logContent.AppendLine($"Summary: {results.Count(r => r.Success)} successful, {results.Count(r => !r.Success)} failed");

        await File.WriteAllTextAsync(logFileName, logContent.ToString());
        logger?.LogInformation($"Detailed results written to: {logFileName}");
    }
}

public class ResetResult
{
    public string Email { get; set; } = string.Empty;
    public bool Success { get; set; }
    public string ErrorMessage { get; set; } = string.Empty;
}